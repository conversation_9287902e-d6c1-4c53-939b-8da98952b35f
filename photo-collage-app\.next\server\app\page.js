/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNXb3JrU3BhY2UlNUNwaG90b0NvbGxhZ2UlNUNwaG90by1jb2xsYWdlLWFwcCU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q1dvcmtTcGFjZSU1Q3Bob3RvQ29sbGFnZSU1Q3Bob3RvLWNvbGxhZ2UtYXBwJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1QixnSkFBdUc7QUFDOUg7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0EseUJBQXlCLG9KQUF5RztBQUNsSSxvQkFBb0IsME5BQWdGO0FBQ3BHO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUM2RDtBQUNwRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDQTtBQUNQO0FBQ0E7QUFDQTtBQUN1RDtBQUN2RDtBQUNPLHdCQUF3Qiw4R0FBa0I7QUFDakQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvP2M0ZmEiXSwic291cmNlc0NvbnRlbnQiOlsiXCJUVVJCT1BBQ0sgeyB0cmFuc2l0aW9uOiBuZXh0LXNzciB9XCI7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcGFnZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcV29ya1NwYWNlXFxcXHBob3RvQ29sbGFnZVxcXFxwaG90by1jb2xsYWdlLWFwcFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpLCBcIkM6XFxcXFdvcmtTcGFjZVxcXFxwaG90b0NvbGxhZ2VcXFxccGhvdG8tY29sbGFnZS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFdvcmtTcGFjZVxcXFxwaG90b0NvbGxhZ2VcXFxccGhvdG8tY29sbGFnZS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpLCBcIkM6XFxcXFdvcmtTcGFjZVxcXFxwaG90b0NvbGxhZ2VcXFxccGhvdG8tY29sbGFnZS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCJdLFxuJ25vdC1mb3VuZCc6IFsoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbiAgICAgICAgXG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxXb3JrU3BhY2VcXFxccGhvdG9Db2xsYWdlXFxcXHBob3RvLWNvbGxhZ2UtYXBwXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9wYWdlXCI7XG5leHBvcnQgY29uc3QgX19uZXh0X2FwcF9fID0ge1xuICAgIHJlcXVpcmU6IF9fbmV4dF9hcHBfcmVxdWlyZV9fLFxuICAgIGxvYWRDaHVuazogX19uZXh0X2FwcF9sb2FkX2NodW5rX19cbn07XG5leHBvcnQgKiBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9hcHAtcmVuZGVyL2VudHJ5LWJhc2VcIjtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiLFxuICAgICAgICBhcHBQYXRoczogW11cbiAgICB9LFxuICAgIHVzZXJsYW5kOiB7XG4gICAgICAgIGxvYWRlclRyZWU6IHRyZWVcbiAgICB9XG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXBhZ2UuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CSimpleEditor.tsx&server=true!":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CSimpleEditor.tsx&server=true! ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SimpleEditor.tsx */ \"(ssr)/./src/components/SimpleEditor.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1dvcmtTcGFjZSU1Q3Bob3RvQ29sbGFnZSU1Q3Bob3RvLWNvbGxhZ2UtYXBwJTVDc3JjJTVDY29tcG9uZW50cyU1Q1NpbXBsZUVkaXRvci50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvP2QzNjkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxXb3JrU3BhY2VcXFxccGhvdG9Db2xsYWdlXFxcXHBob3RvLWNvbGxhZ2UtYXBwXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXFNpbXBsZUVkaXRvci50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CSimpleEditor.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Breadcrumb.tsx":
/*!***************************************!*\
  !*** ./src/components/Breadcrumb.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Home!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Breadcrumb = ({ items, className = \"\" })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        \"aria-label\": \"Breadcrumb\",\n        className: `flex items-center space-x-1 text-sm text-gray-600 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n            className: \"flex items-center space-x-1\",\n            children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                    className: \"flex items-center\",\n                    children: [\n                        index > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4 text-gray-400 mx-1\",\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 15\n                        }, undefined),\n                        item.current ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-gray-900\",\n                            \"aria-current\": \"page\",\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 15\n                        }, undefined) : item.href ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: item.href,\n                            className: \"hover:text-gray-900 transition-colors\",\n                            children: [\n                                index === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Home_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4 mr-1 inline\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 33\n                                }, undefined),\n                                item.label\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: item.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, index, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Breadcrumb.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Breadcrumb);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Breadcrumb.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SimpleEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/SimpleEditor.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/templates */ \"(ssr)/./src/data/templates.ts\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/badge */ \"(ssr)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _StructuredData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./StructuredData */ \"(ssr)/./src/components/StructuredData.tsx\");\n/* harmony import */ var _Breadcrumb__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Breadcrumb */ \"(ssr)/./src/components/Breadcrumb.tsx\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst SimpleEditor = ({ initialTemplateId })=>{\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTemplateId ? _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.find((t)=>t.id === initialTemplateId) || _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates[0] : _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates[0]);\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditor, setShowEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const breadcrumbItems = [\n        {\n            label: \"Home\",\n            href: \"/\"\n        },\n        {\n            label: \"Photo Collage Maker\",\n            current: true\n        }\n    ];\n    const categoryIcons = {\n        grid: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        heart: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        letter: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        number: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        shape: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    };\n    // Photo upload functionality\n    const createPhotoFromFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            const img = new Image();\n            reader.onload = (e)=>{\n                const url = e.target?.result;\n                img.onload = ()=>{\n                    const photo = {\n                        id: `photo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n                        file,\n                        url,\n                        width: img.width,\n                        height: img.height\n                    };\n                    resolve(photo);\n                };\n                img.onerror = ()=>reject(new Error(\"Failed to load image\"));\n                img.src = url;\n            };\n            reader.onerror = ()=>reject(new Error(\"Failed to read file\"));\n            reader.readAsDataURL(file);\n        });\n    }, []);\n    const handleFileSelect = async (e)=>{\n        const files = e.target.files;\n        if (!files || files.length === 0) return;\n        setIsUploading(true);\n        const newPhotos = [];\n        for(let i = 0; i < files.length; i++){\n            try {\n                const photo = await createPhotoFromFile(files[i]);\n                newPhotos.push(photo);\n            } catch (error) {\n                console.error(\"Failed to process image:\", error);\n            }\n        }\n        setUploadedPhotos((prev)=>[\n                ...prev,\n                ...newPhotos\n            ]);\n        setIsUploading(false);\n        // Reset input\n        e.target.value = \"\";\n        // Auto-place photos if template is selected\n        if (selectedTemplate && newPhotos.length > 0) {\n            autoPlacePhotos(newPhotos);\n        }\n    };\n    const autoPlacePhotos = (newPhotos)=>{\n        if (!selectedTemplate) return;\n        const availableSlots = selectedTemplate.slots.filter((slot)=>!placedPhotos.some((placed)=>placed.slotId === slot.id));\n        const newPlacedPhotos = [];\n        newPhotos.forEach((photo, index)=>{\n            if (index < availableSlots.length) {\n                const slot = availableSlots[index];\n                newPlacedPhotos.push({\n                    photoId: photo.id,\n                    slotId: slot.id,\n                    x: 50,\n                    y: 50,\n                    scale: 1.0,\n                    rotation: 0\n                });\n            }\n        });\n        if (newPlacedPhotos.length > 0) {\n            setPlacedPhotos((prev)=>[\n                    ...prev,\n                    ...newPlacedPhotos\n                ]);\n        }\n    };\n    const handleStartEditing = ()=>{\n        if (selectedTemplate) {\n            setShowEditor(true);\n        }\n    };\n    const handleBackToTemplates = ()=>{\n        setShowEditor(false);\n    };\n    // Render editor view if user has started editing\n    if (showEditor && selectedTemplate) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white border-b border-gray-200 px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleBackToTemplates,\n                                            children: \"← Back to Templates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: [\n                                                \"Editing: \",\n                                                selectedTemplate.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Share\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            disabled: placedPhotos.length === 0,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Download\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-[calc(100vh-80px)]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Upload Photos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-8 h-8 text-gray-400 mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-2\",\n                                            children: \"Drag photos here or click to upload\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            onClick: ()=>fileInputRef.current?.click(),\n                                            disabled: isUploading,\n                                            children: isUploading ? \"Uploading...\" : \"Choose Files\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            multiple: true,\n                                            accept: \"image/*\",\n                                            onChange: handleFileSelect,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined),\n                                uploadedPhotos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium mb-2\",\n                                            children: [\n                                                \"Uploaded Photos (\",\n                                                uploadedPhotos.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-2\",\n                                            children: uploadedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: photo.url,\n                                                            alt: \"Uploaded photo\",\n                                                            className: \"w-full h-20 object-cover rounded border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setUploadedPhotos((prev)=>prev.filter((p)=>p.id !== photo.id)),\n                                                            className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, photo.id, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-8 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 text-center\",\n                                        children: [\n                                            selectedTemplate.name,\n                                            \" Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative bg-gray-100 rounded border-2 border-gray-300\",\n                                        style: {\n                                            width: Math.min(600, selectedTemplate.canvasWidth * 0.5),\n                                            height: Math.min(400, selectedTemplate.canvasHeight * 0.5)\n                                        },\n                                        children: selectedTemplate.slots.map((slot)=>{\n                                            const placedPhoto = placedPhotos.find((p)=>p.slotId === slot.id);\n                                            const uploadedPhoto = placedPhoto ? uploadedPhotos.find((p)=>p.id === placedPhoto.photoId) : null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute border-2 border-dashed border-gray-400 bg-gray-50 flex items-center justify-center text-gray-500 text-sm\",\n                                                style: {\n                                                    left: `${slot.x / selectedTemplate.canvasWidth * 100}%`,\n                                                    top: `${slot.y / selectedTemplate.canvasHeight * 100}%`,\n                                                    width: `${slot.width / selectedTemplate.canvasWidth * 100}%`,\n                                                    height: `${slot.height / selectedTemplate.canvasHeight * 100}%`\n                                                },\n                                                children: uploadedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: uploadedPhoto.url,\n                                                    alt: \"Placed photo\",\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Photo \",\n                                                        slot.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, slot.id, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    placedPhotos.length < selectedTemplate.slots.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-gray-600 mt-4\",\n                                        children: [\n                                            \"Upload \",\n                                            selectedTemplate.slots.length - placedPhotos.length,\n                                            \" more photos to fill all slots\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StructuredData__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                type: \"WebApplication\",\n                data: _StructuredData__WEBPACK_IMPORTED_MODULE_6__.WebApplicationSchema\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StructuredData__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                type: \"HowTo\",\n                data: _StructuredData__WEBPACK_IMPORTED_MODULE_6__.HowToSchema\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StructuredData__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                type: \"FAQPage\",\n                data: _StructuredData__WEBPACK_IMPORTED_MODULE_6__.FAQSchema\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 px-4 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Photo Collage Maker\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Share\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            disabled: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Download\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Breadcrumb__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            items: breadcrumbItems\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-6xl mx-auto p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-gray-800 mb-4\",\n                                children: \"Create Beautiful Photo Collages Online Free\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                children: \"Design stunning photo collages with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates. Upload photos and download high-quality collages instantly - no registration required!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ 100% Free\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ No Registration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ High Quality Downloads\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ Multiple Templates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold text-gray-800\",\n                                                children: \"Choose Your Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-1\",\n                                                children: \"Select from our collection of beautiful collage templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"default\",\n                                        className: \"text-sm\",\n                                        children: [\n                                            selectedTemplate.name,\n                                            \" Selected\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                                children: _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.map((template)=>{\n                                    const IconComponent = categoryIcons[template.category] || _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n                                    const isSelected = selectedTemplate?.id === template.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: `cursor-pointer transition-all duration-200 hover:shadow-lg ${isSelected ? \"ring-2 ring-blue-500 ring-offset-2\" : \"\"}`,\n                                        onClick: ()=>setSelectedTemplate(template),\n                                        role: \"button\",\n                                        tabIndex: 0,\n                                        \"aria-label\": `Select ${template.name} template with ${template.slots.length} photo slots`,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg mb-3 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"w-8 h-8 text-gray-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 font-medium\",\n                                                                children: [\n                                                                    template.slots.length,\n                                                                    \" Photos\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 text-sm\",\n                                                            children: template.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600 line-clamp-2\",\n                                                            children: template.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs capitalize\",\n                                                                    children: template.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-blue-600 text-xs font-medium\",\n                                                                    children: \"✓ Selected\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, template.id, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined),\n                    selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                                            children: [\n                                                \"Ready to Create: \",\n                                                selectedTemplate.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: selectedTemplate.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: selectedTemplate.slots.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Photo Slots\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: selectedTemplate.canvasWidth\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Width (px)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: selectedTemplate.canvasHeight\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Height (px)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-orange-600 capitalize\",\n                                                            children: selectedTemplate.category\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3 justify-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setSelectedTemplate(null),\n                                        children: \"Choose Different Template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        className: \"bg-blue-600 hover:bg-blue-700\",\n                                        onClick: handleStartEditing,\n                                        children: \"Start Creating Collage →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-800 mb-6 text-center\",\n                                children: \"How It Works\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-600 font-bold\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Choose Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Select from heart shapes, grids, letters, and numbers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-bold\",\n                                                    children: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Upload Photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Drag and drop or click to upload your favorite photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-purple-600 font-bold\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Arrange & Edit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Position, scale, and rotate photos to perfect your collage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-orange-600 font-bold\",\n                                                    children: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Save your high-quality collage as PNG image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-800 mb-6 text-center\",\n                                children: \"Why Choose Our Collage Maker?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"100% Free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Create unlimited collages without any cost or hidden fees\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Multiple Templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Choose from hearts, grids, letters, numbers, and custom shapes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-8 h-8 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"High Quality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Download your collages in high resolution perfect for printing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SimpleEditor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SimpleEditor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/StructuredData.tsx":
/*!*******************************************!*\
  !*** ./src/components/StructuredData.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreativeWorkSchema: () => (/* binding */ CreativeWorkSchema),\n/* harmony export */   FAQSchema: () => (/* binding */ FAQSchema),\n/* harmony export */   HowToSchema: () => (/* binding */ HowToSchema),\n/* harmony export */   WebApplicationSchema: () => (/* binding */ WebApplicationSchema),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default,WebApplicationSchema,HowToSchema,FAQSchema,CreativeWorkSchema auto */ \nconst StructuredData = ({ type, data })=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const script = document.createElement(\"script\");\n        script.type = \"application/ld+json\";\n        script.text = JSON.stringify({\n            \"@context\": \"https://schema.org\",\n            \"@type\": type,\n            ...data\n        });\n        document.head.appendChild(script);\n        return ()=>{\n            document.head.removeChild(script);\n        };\n    }, [\n        type,\n        data\n    ]);\n    return null;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StructuredData);\n// Predefined structured data for common use cases\nconst WebApplicationSchema = {\n    name: \"Photo Collage Maker\",\n    description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates.\",\n    url: \"https://photocollagemakerpro.com\",\n    applicationCategory: \"DesignApplication\",\n    operatingSystem: \"Web Browser\",\n    offers: {\n        \"@type\": \"Offer\",\n        price: \"0\",\n        priceCurrency: \"USD\",\n        availability: \"https://schema.org/InStock\"\n    },\n    featureList: [\n        \"Free photo collage maker\",\n        \"Multiple template categories\",\n        \"Heart-shaped collages\",\n        \"Grid layouts\",\n        \"Letter and number templates\",\n        \"Drag and drop interface\",\n        \"High-quality downloads\",\n        \"No registration required\"\n    ],\n    screenshot: \"https://photocollagemakerpro.com/screenshot.jpg\",\n    softwareVersion: \"1.0.0\",\n    aggregateRating: {\n        \"@type\": \"AggregateRating\",\n        ratingValue: \"4.8\",\n        ratingCount: \"1250\",\n        bestRating: \"5\",\n        worstRating: \"1\"\n    },\n    author: {\n        \"@type\": \"Organization\",\n        name: \"Photo Collage Maker Team\"\n    },\n    publisher: {\n        \"@type\": \"Organization\",\n        name: \"Photo Collage Maker\",\n        logo: {\n            \"@type\": \"ImageObject\",\n            url: \"https://photocollagemakerpro.com/logo.png\"\n        }\n    }\n};\nconst HowToSchema = {\n    name: \"How to Create a Photo Collage\",\n    description: \"Step-by-step guide to creating beautiful photo collages using our online tool\",\n    image: \"https://photocollagemakerpro.com/how-to-guide.jpg\",\n    totalTime: \"PT5M\",\n    estimatedCost: {\n        \"@type\": \"MonetaryAmount\",\n        currency: \"USD\",\n        value: \"0\"\n    },\n    supply: [\n        {\n            \"@type\": \"HowToSupply\",\n            name: \"Digital Photos\"\n        }\n    ],\n    tool: [\n        {\n            \"@type\": \"HowToTool\",\n            name: \"Photo Collage Maker Web Application\"\n        }\n    ],\n    step: [\n        {\n            \"@type\": \"HowToStep\",\n            name: \"Choose Template\",\n            text: \"Select a template from our gallery of heart shapes, grids, letters, and numbers\",\n            image: \"https://photocollagemakerpro.com/step1.jpg\"\n        },\n        {\n            \"@type\": \"HowToStep\",\n            name: \"Upload Photos\",\n            text: \"Upload your photos by dragging and dropping or clicking to select files\",\n            image: \"https://photocollagemakerpro.com/step2.jpg\"\n        },\n        {\n            \"@type\": \"HowToStep\",\n            name: \"Arrange Photos\",\n            text: \"Drag photos into template slots and adjust position, scale, and rotation\",\n            image: \"https://photocollagemakerpro.com/step3.jpg\"\n        },\n        {\n            \"@type\": \"HowToStep\",\n            name: \"Download Collage\",\n            text: \"Preview your collage and download as a high-quality PNG image\",\n            image: \"https://photocollagemakerpro.com/step4.jpg\"\n        }\n    ]\n};\nconst FAQSchema = {\n    mainEntity: [\n        {\n            \"@type\": \"Question\",\n            name: \"Is the photo collage maker free to use?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"Yes, our photo collage maker is completely free to use. You can create unlimited collages without any cost or registration required.\"\n            }\n        },\n        {\n            \"@type\": \"Question\",\n            name: \"What image formats are supported?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"We support JPEG, PNG, and WebP image formats. Maximum file size is 10MB per image.\"\n            }\n        },\n        {\n            \"@type\": \"Question\",\n            name: \"How many photos can I add to a collage?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"The number of photos depends on the template you choose. Our templates range from 4 photos to 16 photos per collage.\"\n            }\n        },\n        {\n            \"@type\": \"Question\",\n            name: \"Can I download my collage in high quality?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"Yes, you can download your finished collage as a high-quality PNG image that's perfect for printing or sharing online.\"\n            }\n        },\n        {\n            \"@type\": \"Question\",\n            name: \"Do I need to create an account?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"No account creation is required. You can start creating collages immediately without any registration.\"\n            }\n        }\n    ]\n};\nconst CreativeWorkSchema = {\n    name: \"Photo Collage Templates\",\n    description: \"Collection of creative photo collage templates including hearts, grids, letters, and shapes\",\n    creator: {\n        \"@type\": \"Organization\",\n        name: \"Photo Collage Maker Team\"\n    },\n    dateCreated: \"2024-01-01\",\n    dateModified: new Date().toISOString().split(\"T\")[0],\n    genre: \"Design Template\",\n    keywords: [\n        \"photo collage\",\n        \"template\",\n        \"heart shape\",\n        \"grid layout\",\n        \"letter template\",\n        \"number template\",\n        \"photo arrangement\"\n    ],\n    license: \"https://creativecommons.org/licenses/by/4.0/\",\n    usageInfo: \"Free for personal and commercial use\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/StructuredData.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/data/templates.ts":
/*!*******************************!*\
  !*** ./src/data/templates.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTemplateById: () => (/* binding */ getTemplateById),\n/* harmony export */   getTemplatesByCategory: () => (/* binding */ getTemplatesByCategory),\n/* harmony export */   templates: () => (/* binding */ templates)\n/* harmony export */ });\nconst templates = [\n    // Grid Layout Template\n    {\n        id: \"grid-4x4\",\n        name: \"4x4 Grid\",\n        description: \"Classic 16-photo grid layout\",\n        category: \"grid\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 800,\n        backgroundColor: \"#ffffff\",\n        slots: [\n            // Row 1\n            {\n                id: \"slot-1\",\n                x: 2,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-2\",\n                x: 27,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-3\",\n                x: 52,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-4\",\n                x: 77,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 2\n            {\n                id: \"slot-5\",\n                x: 2,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-6\",\n                x: 27,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-7\",\n                x: 52,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-8\",\n                x: 77,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 3\n            {\n                id: \"slot-9\",\n                x: 2,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-10\",\n                x: 27,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-11\",\n                x: 52,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-12\",\n                x: 77,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 4\n            {\n                id: \"slot-13\",\n                x: 2,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-14\",\n                x: 27,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-15\",\n                x: 52,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-16\",\n                x: 77,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Heart Shape Template\n    {\n        id: \"heart-shape\",\n        name: \"Heart Collage\",\n        description: \"Romantic heart-shaped photo arrangement\",\n        category: \"heart\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 700,\n        backgroundColor: \"#ffe6f2\",\n        slots: [\n            // Top left curve\n            {\n                id: \"heart-1\",\n                x: 15,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-2\",\n                x: 32,\n                y: 10,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-3\",\n                x: 10,\n                y: 32,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            // Top right curve\n            {\n                id: \"heart-4\",\n                x: 55,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-5\",\n                x: 72,\n                y: 10,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-6\",\n                x: 78,\n                y: 32,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            // Center area\n            {\n                id: \"heart-7\",\n                x: 35,\n                y: 35,\n                width: 30,\n                height: 20,\n                shape: \"rectangle\"\n            },\n            // Lower sections\n            {\n                id: \"heart-8\",\n                x: 25,\n                y: 58,\n                width: 18,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"heart-9\",\n                x: 57,\n                y: 58,\n                width: 18,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"heart-10\",\n                x: 42,\n                y: 75,\n                width: 16,\n                height: 12,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Letter 'A' Template\n    {\n        id: \"letter-a\",\n        name: \"Letter A\",\n        description: \"Letter A shaped photo collage\",\n        category: \"letter\",\n        thumbnail: \"\",\n        canvasWidth: 600,\n        canvasHeight: 800,\n        backgroundColor: \"#f0f8ff\",\n        slots: [\n            // Top point\n            {\n                id: \"a-top\",\n                x: 45,\n                y: 5,\n                width: 10,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Upper left diagonal\n            {\n                id: \"a-ul1\",\n                x: 35,\n                y: 22,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: -15\n            },\n            {\n                id: \"a-ul2\",\n                x: 25,\n                y: 38,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: -15\n            },\n            // Upper right diagonal\n            {\n                id: \"a-ur1\",\n                x: 53,\n                y: 22,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: 15\n            },\n            {\n                id: \"a-ur2\",\n                x: 63,\n                y: 38,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: 15\n            },\n            // Cross bar\n            {\n                id: \"a-cross1\",\n                x: 35,\n                y: 50,\n                width: 12,\n                height: 8,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-cross2\",\n                x: 53,\n                y: 50,\n                width: 12,\n                height: 8,\n                shape: \"rectangle\"\n            },\n            // Lower left leg\n            {\n                id: \"a-ll1\",\n                x: 15,\n                y: 65,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-ll2\",\n                x: 15,\n                y: 82,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Lower right leg\n            {\n                id: \"a-lr1\",\n                x: 73,\n                y: 65,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-lr2\",\n                x: 73,\n                y: 82,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Number '1' Template\n    {\n        id: \"number-1\",\n        name: \"Number 1\",\n        description: \"Number 1 shaped photo collage\",\n        category: \"number\",\n        thumbnail: \"\",\n        canvasWidth: 400,\n        canvasHeight: 800,\n        backgroundColor: \"#fff5ee\",\n        slots: [\n            // Top diagonal\n            {\n                id: \"num1-top\",\n                x: 25,\n                y: 5,\n                width: 15,\n                height: 12,\n                shape: \"rectangle\",\n                rotation: 45\n            },\n            // Main vertical line\n            {\n                id: \"num1-1\",\n                x: 40,\n                y: 15,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-2\",\n                x: 40,\n                y: 32,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-3\",\n                x: 40,\n                y: 49,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-4\",\n                x: 40,\n                y: 66,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Bottom base\n            {\n                id: \"num1-base1\",\n                x: 20,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-base2\",\n                x: 40,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-base3\",\n                x: 60,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Circular Pattern Template\n    {\n        id: \"circle-pattern\",\n        name: \"Circle Pattern\",\n        description: \"Circular arrangement of photos\",\n        category: \"shape\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 800,\n        backgroundColor: \"#f5f5f5\",\n        slots: [\n            // Center circle\n            {\n                id: \"center\",\n                x: 37.5,\n                y: 37.5,\n                width: 25,\n                height: 25,\n                shape: \"circle\"\n            },\n            // Inner ring (8 photos)\n            {\n                id: \"inner-1\",\n                x: 50,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-2\",\n                x: 70,\n                y: 25,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-3\",\n                x: 80,\n                y: 50,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-4\",\n                x: 70,\n                y: 75,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-5\",\n                x: 50,\n                y: 85,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-6\",\n                x: 25,\n                y: 75,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-7\",\n                x: 15,\n                y: 50,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-8\",\n                x: 25,\n                y: 25,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            // Outer ring (4 photos)\n            {\n                id: \"outer-1\",\n                x: 50,\n                y: 2,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-2\",\n                x: 88,\n                y: 50,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-3\",\n                x: 50,\n                y: 88,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-4\",\n                x: 2,\n                y: 50,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            }\n        ]\n    }\n];\nconst getTemplateById = (id)=>{\n    return templates.find((template)=>template.id === id);\n};\nconst getTemplatesByCategory = (category)=>{\n    return templates.filter((template)=>template.category === category);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/data/templates.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzRlZTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2OTQwYzc3YzkwNTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: {\n        default: \"Photo Collage Maker - Create Beautiful Collages Online Free\",\n        template: \"%s | Photo Collage Maker\"\n    },\n    description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates. Upload photos and download high-quality collages instantly.\",\n    keywords: [\n        \"photo collage maker\",\n        \"collage creator\",\n        \"photo editor\",\n        \"free collage maker\",\n        \"online photo collage\",\n        \"picture collage\",\n        \"photo montage\",\n        \"template collage\",\n        \"heart collage\",\n        \"grid collage\",\n        \"letter collage\",\n        \"number collage\"\n    ],\n    authors: [\n        {\n            name: \"Photo Collage Maker Team\"\n        }\n    ],\n    creator: \"Photo Collage Maker\",\n    publisher: \"Photo Collage Maker\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://photocollagemakerpro.com\"),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"/\",\n        title: \"Photo Collage Maker - Create Beautiful Collages Online Free\",\n        description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates.\",\n        siteName: \"Photo Collage Maker\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Photo Collage Maker - Create Beautiful Collages Online\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Photo Collage Maker - Create Beautiful Collages Online Free\",\n        description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates.\",\n        images: [\n            \"/twitter-image.jpg\"\n        ],\n        creator: \"@photocollagemakerpro\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\",\n        yandex: \"your-yandex-verification-code\",\n        yahoo: \"your-yahoo-verification-code\"\n    }\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 5,\n    userScalable: true,\n    themeColor: [\n        {\n            media: \"(prefers-color-scheme: light)\",\n            color: \"#ffffff\"\n        },\n        {\n            media: \"(prefers-color-scheme: dark)\",\n            color: \"#000000\"\n        }\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebApplication\",\n                                name: \"Photo Collage Maker\",\n                                description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates.\",\n                                url: \"https://photocollagemakerpro.com\",\n                                applicationCategory: \"DesignApplication\",\n                                operatingSystem: \"Web Browser\",\n                                offers: {\n                                    \"@type\": \"Offer\",\n                                    price: \"0\",\n                                    priceCurrency: \"USD\",\n                                    availability: \"https://schema.org/InStock\"\n                                },\n                                featureList: [\n                                    \"Free photo collage maker\",\n                                    \"Multiple template categories\",\n                                    \"Heart-shaped collages\",\n                                    \"Grid layouts\",\n                                    \"Letter and number templates\",\n                                    \"Drag and drop interface\",\n                                    \"High-quality downloads\",\n                                    \"No registration required\"\n                                ],\n                                screenshot: \"https://photocollagemakerpro.com/screenshot.jpg\",\n                                softwareVersion: \"1.0.0\",\n                                aggregateRating: {\n                                    \"@type\": \"AggregateRating\",\n                                    ratingValue: \"4.8\",\n                                    ratingCount: \"1250\",\n                                    bestRating: \"5\",\n                                    worstRating: \"1\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} antialiased`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"root\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_SimpleEditor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/SimpleEditor */ \"(rsc)/./src/components/SimpleEditor.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SimpleEditor__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXFEO0FBRXRDLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztrQkFDQyw0RUFBQ0YsZ0VBQVlBOzs7Ozs7Ozs7O0FBR25CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvLi9zcmMvYXBwL3BhZ2UudHN4P2Y2OGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFNpbXBsZUVkaXRvciBmcm9tICdAL2NvbXBvbmVudHMvU2ltcGxlRWRpdG9yJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8bWFpbj5cbiAgICAgIDxTaW1wbGVFZGl0b3IgLz5cbiAgICA8L21haW4+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiU2ltcGxlRWRpdG9yIiwiSG9tZSIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SimpleEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/SimpleEditor.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\WorkSpace\photoCollage\photo-collage-app\src\components\SimpleEditor.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();