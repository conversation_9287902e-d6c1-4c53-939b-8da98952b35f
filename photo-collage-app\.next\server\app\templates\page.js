/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/templates/page";
exports.ids = ["app/templates/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftemplates%2Fpage&page=%2Ftemplates%2Fpage&appPaths=%2Ftemplates%2Fpage&pagePath=private-next-app-dir%2Ftemplates%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftemplates%2Fpage&page=%2Ftemplates%2Fpage&appPaths=%2Ftemplates%2Fpage&pagePath=private-next-app-dir%2Ftemplates%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'templates',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/templates/page.tsx */ \"(rsc)/./src/app/templates/page.tsx\")), \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/templates/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/templates/page\",\n        pathname: \"/templates\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftemplates%2Fpage&page=%2Ftemplates%2Fpage&appPaths=%2Ftemplates%2Fpage&pagePath=private-next-app-dir%2Ftemplates%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!******************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1dvcmtTcGFjZSU1Q3Bob3RvQ29sbGFnZSU1Q3Bob3RvLWNvbGxhZ2UtYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNsaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLz80MjBhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcV29ya1NwYWNlXFxcXHBob3RvQ29sbGFnZVxcXFxwaG90by1jb2xsYWdlLWFwcFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CFooter.tsx&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CNavigation.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CFooter.tsx&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CNavigation.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer.tsx */ \"(ssr)/./src/components/Footer.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navigation.tsx */ \"(ssr)/./src/components/Navigation.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1dvcmtTcGFjZSU1Q3Bob3RvQ29sbGFnZSU1Q3Bob3RvLWNvbGxhZ2UtYXBwJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCUyQyUyMmRpc3BsYXklMjIlM0ElMjJzd2FwJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz1DJTNBJTVDV29ya1NwYWNlJTVDcGhvdG9Db2xsYWdlJTVDcGhvdG8tY29sbGFnZS1hcHAlNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNXb3JrU3BhY2UlNUNwaG90b0NvbGxhZ2UlNUNwaG90by1jb2xsYWdlLWFwcCU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNGb290ZXIudHN4Jm1vZHVsZXM9QyUzQSU1Q1dvcmtTcGFjZSU1Q3Bob3RvQ29sbGFnZSU1Q3Bob3RvLWNvbGxhZ2UtYXBwJTVDc3JjJTVDY29tcG9uZW50cyU1Q05hdmlnYXRpb24udHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBZ0g7QUFDaEgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9waG90by1jb2xsYWdlLWFwcC8/ZjBkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFdvcmtTcGFjZVxcXFxwaG90b0NvbGxhZ2VcXFxccGhvdG8tY29sbGFnZS1hcHBcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcRm9vdGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcV29ya1NwYWNlXFxcXHBob3RvQ29sbGFnZVxcXFxwaG90by1jb2xsYWdlLWFwcFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxOYXZpZ2F0aW9uLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CFooter.tsx&modules=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Ccomponents%5CNavigation.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Type!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const categoryLinks = [\n        {\n            href: \"/templates/grid\",\n            label: \"Grid Templates\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        },\n        {\n            href: \"/templates/heart\",\n            label: \"Heart Templates\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        },\n        {\n            href: \"/templates/letter\",\n            label: \"Letter Templates\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            href: \"/templates/number\",\n            label: \"Number Templates\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            href: \"/templates/shape\",\n            label: \"Shape Templates\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"Photo Collage Maker Pro\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-4 text-gray-400 text-sm\",\n                                    children: \"Create beautiful photo collages online for free. Choose from 50+ templates and download high-quality results instantly.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Templates\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"All Templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        categoryLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: link.href,\n                                                    className: \"text-gray-400 hover:text-white transition-colors\",\n                                                    children: link.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, link.href, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Home\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/faq\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"FAQ\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Browse Templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Create Collage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Popular Templates\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates/grid/grid-4x4\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"4\\xd74 Grid\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates/heart/heart-classic\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Classic Heart\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates/letter/letter-a\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Letter A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/templates/number/number-1\",\n                                                className: \"text-gray-400 hover:text-white transition-colors\",\n                                                children: \"Number 1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-gray-800 mt-12 pt-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-sm\",\n                                children: [\n                                    \"\\xa9 \",\n                                    currentYear,\n                                    \" Photo Collage Maker Pro. All rights reserved.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/sitemap.xml\",\n                                                className: \"text-gray-400 hover:text-white transition-colors text-sm\",\n                                                children: \"Sitemap\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"/faq\",\n                                                className: \"text-gray-400 hover:text-white transition-colors text-sm\",\n                                                children: \"Help\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Menu,Type,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Menu,Type,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Menu,Type,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Menu,Type,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Menu,Type,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Menu,Type,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Menu,Type,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Navigation = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const categoryLinks = [\n        {\n            href: \"/templates/grid\",\n            label: \"Grid Templates\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            href: \"/templates/heart\",\n            label: \"Heart Templates\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            href: \"/templates/letter\",\n            label: \"Letter Templates\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            href: \"/templates/number\",\n            label: \"Number Templates\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            href: \"/templates/shape\",\n            label: \"Shape Templates\",\n            icon: _barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-white shadow-sm border-b\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/\",\n                            className: \"flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold text-blue-600\",\n                                children: \"Photo Collage Maker Pro\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"text-gray-700 hover:text-blue-600 transition-colors\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/templates\",\n                                            className: \"text-gray-700 hover:text-blue-600 transition-colors\",\n                                            children: \"Templates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"py-2\",\n                                                children: categoryLinks.map((link)=>{\n                                                    const IconComponent = link.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: link.href,\n                                                        className: \"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"w-4 h-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                                                lineNumber: 48,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            link.label\n                                                        ]\n                                                    }, link.href, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                                        lineNumber: 43,\n                                                        columnNumber: 23\n                                                    }, undefined);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/faq\",\n                                    className: \"text-gray-700 hover:text-blue-600 transition-colors\",\n                                    children: \"FAQ\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/\",\n                                        children: \"Create Collage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 29\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Grid_Hash_Heart_Menu_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 57\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, undefined),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"block px-3 py-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"Home\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/templates\",\n                                className: \"block px-3 py-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"All Templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 15\n                            }, undefined),\n                            categoryLinks.map((link)=>{\n                                const IconComponent = link.icon;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: link.href,\n                                    className: \"flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        link.label\n                                    ]\n                                }, link.href, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 19\n                                }, undefined);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/faq\",\n                                className: \"block px-3 py-2 text-gray-700 hover:text-blue-600 transition-colors\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: \"FAQ\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    asChild: true,\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: \"Create Collage\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\Navigation.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QiwwUkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUssdUJBQVNsQiw2Q0FBZ0IsQ0FDN0IsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFZCxPQUFPLEVBQUVPLElBQUksRUFBRVEsVUFBVSxLQUFLLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4RCxNQUFNQyxPQUFPSCxVQUFVcEIsc0RBQUlBLEdBQUc7SUFDOUIscUJBQ0UsOERBQUN1QjtRQUNDSixXQUFXakIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU87UUFBVTtRQUN4REcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixPQUFPTyxXQUFXLEdBQUc7QUFFWSIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLy4vc3JjL2NvbXBvbmVudHMvdWkvYnV0dG9uLnRzeD82YTBhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBTbG90IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zbG90XCJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBidXR0b25WYXJpYW50cyA9IGN2YShcbiAgXCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgd2hpdGVzcGFjZS1ub3dyYXAgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgdHJhbnNpdGlvbi1jb2xvcnMgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxuICB7XG4gICAgdmFyaWFudHM6IHtcbiAgICAgIHZhcmlhbnQ6IHtcbiAgICAgICAgZGVmYXVsdDogXCJiZy1wcmltYXJ5IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIGhvdmVyOmJnLXByaW1hcnkvOTBcIixcbiAgICAgICAgZGVzdHJ1Y3RpdmU6XG4gICAgICAgICAgXCJiZy1kZXN0cnVjdGl2ZSB0ZXh0LWRlc3RydWN0aXZlLWZvcmVncm91bmQgaG92ZXI6YmctZGVzdHJ1Y3RpdmUvOTBcIixcbiAgICAgICAgb3V0bGluZTpcbiAgICAgICAgICBcImJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBzZWNvbmRhcnk6XG4gICAgICAgICAgXCJiZy1zZWNvbmRhcnkgdGV4dC1zZWNvbmRhcnktZm9yZWdyb3VuZCBob3ZlcjpiZy1zZWNvbmRhcnkvODBcIixcbiAgICAgICAgZ2hvc3Q6IFwiaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmRcIixcbiAgICAgICAgbGluazogXCJ0ZXh0LXByaW1hcnkgdW5kZXJsaW5lLW9mZnNldC00IGhvdmVyOnVuZGVybGluZVwiLFxuICAgICAgfSxcbiAgICAgIHNpemU6IHtcbiAgICAgICAgZGVmYXVsdDogXCJoLTEwIHB4LTQgcHktMlwiLFxuICAgICAgICBzbTogXCJoLTkgcm91bmRlZC1tZCBweC0zXCIsXG4gICAgICAgIGxnOiBcImgtMTEgcm91bmRlZC1tZCBweC04XCIsXG4gICAgICAgIGljb246IFwiaC0xMCB3LTEwXCIsXG4gICAgICB9LFxuICAgIH0sXG4gICAgZGVmYXVsdFZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcbiAgICAgIHNpemU6IFwiZGVmYXVsdFwiLFxuICAgIH0sXG4gIH1cbilcblxuZXhwb3J0IGludGVyZmFjZSBCdXR0b25Qcm9wc1xuICBleHRlbmRzIFJlYWN0LkJ1dHRvbkhUTUxBdHRyaWJ1dGVzPEhUTUxCdXR0b25FbGVtZW50PixcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGJ1dHRvblZhcmlhbnRzPiB7XG4gIGFzQ2hpbGQ/OiBib29sZWFuXG59XG5cbmNvbnN0IEJ1dHRvbiA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTEJ1dHRvbkVsZW1lbnQsIEJ1dHRvblByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCB2YXJpYW50LCBzaXplLCBhc0NoaWxkID0gZmFsc2UsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IFwiYnV0dG9uXCJcbiAgICByZXR1cm4gKFxuICAgICAgPENvbXBcbiAgICAgICAgY2xhc3NOYW1lPXtjbihidXR0b25WYXJpYW50cyh7IHZhcmlhbnQsIHNpemUsIGNsYXNzTmFtZSB9KSl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuQnV0dG9uLmRpc3BsYXlOYW1lID0gXCJCdXR0b25cIlxuXG5leHBvcnQgeyBCdXR0b24sIGJ1dHRvblZhcmlhbnRzIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlNsb3QiLCJjdmEiLCJjbiIsImJ1dHRvblZhcmlhbnRzIiwidmFyaWFudHMiLCJ2YXJpYW50IiwiZGVmYXVsdCIsImRlc3RydWN0aXZlIiwib3V0bGluZSIsInNlY29uZGFyeSIsImdob3N0IiwibGluayIsInNpemUiLCJzbSIsImxnIiwiaWNvbiIsImRlZmF1bHRWYXJpYW50cyIsIkJ1dHRvbiIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJhc0NoaWxkIiwicHJvcHMiLCJyZWYiLCJDb21wIiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcGhvdG8tY29sbGFnZS1hcHAvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzRlZTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI2OTQwYzc3YzkwNTdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navigation */ \"(rsc)/./src/components/Navigation.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Photo Collage Maker - Create Beautiful Collages Online Free | 2024\",\n        template: \"%s | Photo Collage Maker Pro\"\n    },\n    description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from 50+ templates including heart shapes, grids, letters, numbers and custom layouts. Upload photos and download high-quality collages instantly. Perfect for social media, gifts, and memories.\",\n    keywords: [\n        \"photo collage maker\",\n        \"free collage maker online\",\n        \"photo collage creator\",\n        \"collage editor\",\n        \"photo montage maker\",\n        \"picture collage online\",\n        \"heart photo collage\",\n        \"grid photo collage\",\n        \"letter photo collage\",\n        \"number photo collage\",\n        \"instagram collage maker\",\n        \"social media collage\",\n        \"photo layout maker\",\n        \"collage template free\",\n        \"online photo editor\",\n        \"photo arrangement tool\"\n    ],\n    authors: [\n        {\n            name: \"Photo Collage Maker Team\"\n        }\n    ],\n    creator: \"Photo Collage Maker\",\n    publisher: \"Photo Collage Maker\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://photocollagemakerpro.com\"),\n    alternates: {\n        canonical: \"/\"\n    },\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: \"/\",\n        title: \"Photo Collage Maker - Create Beautiful Collages Online Free | 2024\",\n        description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from 50+ templates including heart shapes, grids, letters, numbers and custom layouts. Perfect for social media, gifts, and memories.\",\n        siteName: \"Photo Collage Maker Pro\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Photo Collage Maker Pro - Create Beautiful Collages Online Free\"\n            }\n        ]\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Photo Collage Maker - Create Beautiful Collages Online Free | 2024\",\n        description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from 50+ templates including heart shapes, grids, letters, numbers and custom layouts.\",\n        images: [\n            \"/twitter-image.jpg\"\n        ],\n        creator: \"@photocollagemakerpro\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: \"your-google-verification-code\",\n        yandex: \"your-yandex-verification-code\",\n        yahoo: \"your-yahoo-verification-code\"\n    }\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 5,\n    userScalable: true,\n    themeColor: [\n        {\n            media: \"(prefers-color-scheme: light)\",\n            color: \"#ffffff\"\n        },\n        {\n            media: \"(prefers-color-scheme: dark)\",\n            color: \"#000000\"\n        }\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                        type: \"application/ld+json\",\n                        dangerouslySetInnerHTML: {\n                            __html: JSON.stringify({\n                                \"@context\": \"https://schema.org\",\n                                \"@type\": \"WebApplication\",\n                                name: \"Photo Collage Maker Pro\",\n                                description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from 50+ templates including heart shapes, grids, letters, numbers and custom layouts. Perfect for social media, gifts, and memories.\",\n                                url: \"https://photocollagemakerpro.com\",\n                                applicationCategory: \"DesignApplication\",\n                                operatingSystem: \"Web Browser\",\n                                offers: {\n                                    \"@type\": \"Offer\",\n                                    price: \"0\",\n                                    priceCurrency: \"USD\",\n                                    availability: \"https://schema.org/InStock\"\n                                },\n                                featureList: [\n                                    \"Free photo collage maker\",\n                                    \"50+ professional templates\",\n                                    \"Heart-shaped collages\",\n                                    \"Grid layouts (2x2, 3x3, 4x4)\",\n                                    \"Letter and number templates\",\n                                    \"Drag and drop interface\",\n                                    \"High-quality PNG downloads\",\n                                    \"No registration required\",\n                                    \"Mobile-friendly editor\",\n                                    \"Social media optimized sizes\"\n                                ],\n                                screenshot: \"https://photocollagemakerpro.com/screenshot.jpg\",\n                                softwareVersion: \"1.0.0\",\n                                aggregateRating: {\n                                    \"@type\": \"AggregateRating\",\n                                    ratingValue: \"4.8\",\n                                    ratingCount: \"1250\",\n                                    bestRating: \"5\",\n                                    worstRating: \"1\"\n                                }\n                            })\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\",\n                        sizes: \"any\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/icon.svg\",\n                        type: \"image/svg+xml\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/templates/page.tsx":
/*!************************************!*\
  !*** ./src/app/templates/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TemplatesPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/templates */ \"(rsc)/./src/data/templates.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(rsc)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Type!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Type!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Type!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Type!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Grid,Hash,Heart,Type!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n\n\n\n\n\n\nconst categoryIcons = {\n    grid: _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n    heart: _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n    letter: _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n    number: _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    shape: _barrel_optimize_names_Circle_Grid_Hash_Heart_Type_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n};\nconst categoryDescriptions = {\n    grid: \"Perfect grid layouts for organized photo displays\",\n    heart: \"Romantic heart-shaped collages for special occasions\",\n    letter: \"Spell out names and messages with letter-shaped layouts\",\n    number: \"Celebrate milestones with number-shaped collages\",\n    shape: \"Creative geometric shapes for artistic arrangements\"\n};\nconst metadata = {\n    title: \"Photo Collage Templates - Free Templates for Every Occasion\",\n    description: \"Browse our collection of 50+ free photo collage templates. Choose from heart shapes, grids, letters, numbers, and creative layouts. Perfect for social media, gifts, and memories.\",\n    keywords: [\n        \"photo collage templates\",\n        \"free collage templates\",\n        \"collage layouts\",\n        \"photo template gallery\",\n        \"heart collage templates\",\n        \"grid collage templates\",\n        \"letter collage templates\",\n        \"number collage templates\"\n    ],\n    openGraph: {\n        title: \"Photo Collage Templates - Free Templates for Every Occasion\",\n        description: \"Browse our collection of 50+ free photo collage templates. Choose from heart shapes, grids, letters, numbers, and creative layouts.\",\n        url: \"https://photocollagemakerpro.com/templates\",\n        images: [\n            {\n                url: \"https://photocollagemakerpro.com/templates-og.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"Photo Collage Templates Gallery\"\n            }\n        ]\n    },\n    alternates: {\n        canonical: \"https://photocollagemakerpro.com/templates\"\n    }\n};\nfunction TemplatesPage() {\n    // Group templates by category\n    const templatesByCategory = _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.reduce((acc, template)=>{\n        if (!acc[template.category]) {\n            acc[template.category] = [];\n        }\n        acc[template.category].push(template);\n        return acc;\n    }, {});\n    // Structured data for the templates page\n    const structuredData = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"CollectionPage\",\n        name: \"Photo Collage Templates\",\n        description: \"Browse our collection of 50+ free photo collage templates. Choose from heart shapes, grids, letters, numbers, and creative layouts.\",\n        url: \"https://photocollagemakerpro.com/templates\",\n        mainEntity: {\n            \"@type\": \"ItemList\",\n            numberOfItems: _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.length,\n            itemListElement: Object.entries(templatesByCategory).map(([category, categoryTemplates], index)=>({\n                    \"@type\": \"CreativeWork\",\n                    position: index + 1,\n                    name: `${category.charAt(0).toUpperCase() + category.slice(1)} Templates`,\n                    description: categoryDescriptions[category],\n                    category: category,\n                    url: `https://photocollagemakerpro.com/templates/${category}`,\n                    numberOfItems: categoryTemplates.length\n                }))\n        },\n        breadcrumb: {\n            \"@type\": \"BreadcrumbList\",\n            itemListElement: [\n                {\n                    \"@type\": \"ListItem\",\n                    position: 1,\n                    name: \"Home\",\n                    item: \"https://photocollagemakerpro.com\"\n                },\n                {\n                    \"@type\": \"ListItem\",\n                    position: 2,\n                    name: \"Templates\",\n                    item: \"https://photocollagemakerpro.com/templates\"\n                }\n            ]\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                type: \"application/ld+json\",\n                dangerouslySetInnerHTML: {\n                    __html: JSON.stringify(structuredData)\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-gray-800 mb-4\",\n                                children: \"Photo Collage Templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: [\n                                    \"Browse our collection of \",\n                                    _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.length,\n                                    \"+ free photo collage templates. Choose from heart shapes, grids, letters, numbers, and creative layouts perfect for any occasion.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-12\",\n                        children: Object.entries(templatesByCategory).map(([category, categoryTemplates])=>{\n                            const IconComponent = categoryIcons[category];\n                            const categoryName = category.charAt(0).toUpperCase() + category.slice(1);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    IconComponent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"w-8 h-8 text-blue-600 mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 39\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-bold text-gray-800\",\n                                                        children: [\n                                                            categoryName,\n                                                            \" Templates\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"ml-3\",\n                                                        children: [\n                                                            categoryTemplates.length,\n                                                            \" templates\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: `/templates/${category}`,\n                                                className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                                                children: \"View All →\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-6\",\n                                        children: categoryDescriptions[category]\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\",\n                                        children: categoryTemplates.slice(0, 6).map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: `/templates/${category}/${template.id}`,\n                                                className: \"group\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                    className: \"h-full hover:shadow-lg transition-shadow\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                        className: \"p-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"aspect-square bg-gray-100 rounded mb-2 flex items-center justify-center group-hover:bg-gray-200 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 text-center px-1\",\n                                                                    children: template.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                                    lineNumber: 165,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-sm font-medium text-center truncate\",\n                                                                children: template.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 text-center mt-1\",\n                                                                children: [\n                                                                    template.slots.length,\n                                                                    \" photos\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, template.id, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this),\n                                    categoryTemplates.length > 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: `/templates/${category}`,\n                                            className: \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: [\n                                                \"View All \",\n                                                categoryTemplates.length,\n                                                \" \",\n                                                categoryName,\n                                                \" Templates\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, category, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-8 shadow-sm max-w-2xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold mb-4\",\n                                    children: \"Ready to Create Your Collage?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: \"Choose any template above to start creating your personalized photo collage. It's free, easy, and takes just minutes!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium\",\n                                    children: \"Start Creating Now\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 max-w-4xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg p-8 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold mb-6\",\n                                    children: \"About Our Photo Collage Templates\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mb-4\",\n                                            children: [\n                                                \"Our collection of \",\n                                                _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.length,\n                                                \"+ free photo collage templates offers something for every occasion and style. Whether you're creating a romantic heart collage for Valentine's Day, organizing family photos in a grid layout, or spelling out a special message with letter templates, we have the perfect design for you.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-3\",\n                                            children: \"Template Categories\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"list-disc list-inside space-y-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Grid Templates:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Perfect for organizing multiple photos in clean, symmetrical layouts\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Heart Templates:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Romantic designs ideal for anniversaries, weddings, and Valentine's Day\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Letter Templates:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Spell out names, initials, or messages with photo-filled letters\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Number Templates:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Celebrate birthdays, anniversaries, and milestones with number shapes\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Shape Templates:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                            lineNumber: 229,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Creative geometric and artistic layouts for unique presentations\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"All templates are completely free to use and require no registration. Simply choose your favorite design, upload your photos, and download your finished collage in high quality.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\app\\\\templates\\\\page.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/templates/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\WorkSpace\photoCollage\photo-collage-app\src\components\Footer.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Navigation.tsx":
/*!***************************************!*\
  !*** ./src/components/Navigation.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\WorkSpace\photoCollage\photo-collage-app\src\components\Navigation.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/data/templates.ts":
/*!*******************************!*\
  !*** ./src/data/templates.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getTemplateById: () => (/* binding */ getTemplateById),\n/* harmony export */   getTemplatesByCategory: () => (/* binding */ getTemplatesByCategory),\n/* harmony export */   templates: () => (/* binding */ templates)\n/* harmony export */ });\nconst templates = [\n    // Grid Layout Template\n    {\n        id: \"grid-4x4\",\n        name: \"4x4 Grid\",\n        description: \"Classic 16-photo grid layout\",\n        category: \"grid\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 800,\n        backgroundColor: \"#ffffff\",\n        slots: [\n            // Row 1\n            {\n                id: \"slot-1\",\n                x: 2,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-2\",\n                x: 27,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-3\",\n                x: 52,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-4\",\n                x: 77,\n                y: 2,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 2\n            {\n                id: \"slot-5\",\n                x: 2,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-6\",\n                x: 27,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-7\",\n                x: 52,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-8\",\n                x: 77,\n                y: 27,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 3\n            {\n                id: \"slot-9\",\n                x: 2,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-10\",\n                x: 27,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-11\",\n                x: 52,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-12\",\n                x: 77,\n                y: 52,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            // Row 4\n            {\n                id: \"slot-13\",\n                x: 2,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-14\",\n                x: 27,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-15\",\n                x: 52,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"slot-16\",\n                x: 77,\n                y: 77,\n                width: 21,\n                height: 21,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Heart Shape Template\n    {\n        id: \"heart-shape\",\n        name: \"Heart Collage\",\n        description: \"Romantic heart-shaped photo arrangement\",\n        category: \"heart\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 700,\n        backgroundColor: \"#ffe6f2\",\n        slots: [\n            // Top left curve\n            {\n                id: \"heart-1\",\n                x: 15,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-2\",\n                x: 32,\n                y: 10,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-3\",\n                x: 10,\n                y: 32,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            // Top right curve\n            {\n                id: \"heart-4\",\n                x: 55,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-5\",\n                x: 72,\n                y: 10,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"heart-6\",\n                x: 78,\n                y: 32,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            // Center area\n            {\n                id: \"heart-7\",\n                x: 35,\n                y: 35,\n                width: 30,\n                height: 20,\n                shape: \"rectangle\"\n            },\n            // Lower sections\n            {\n                id: \"heart-8\",\n                x: 25,\n                y: 58,\n                width: 18,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"heart-9\",\n                x: 57,\n                y: 58,\n                width: 18,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"heart-10\",\n                x: 42,\n                y: 75,\n                width: 16,\n                height: 12,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Letter 'A' Template\n    {\n        id: \"letter-a\",\n        name: \"Letter A\",\n        description: \"Letter A shaped photo collage\",\n        category: \"letter\",\n        thumbnail: \"\",\n        canvasWidth: 600,\n        canvasHeight: 800,\n        backgroundColor: \"#f0f8ff\",\n        slots: [\n            // Top point\n            {\n                id: \"a-top\",\n                x: 45,\n                y: 5,\n                width: 10,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Upper left diagonal\n            {\n                id: \"a-ul1\",\n                x: 35,\n                y: 22,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: -15\n            },\n            {\n                id: \"a-ul2\",\n                x: 25,\n                y: 38,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: -15\n            },\n            // Upper right diagonal\n            {\n                id: \"a-ur1\",\n                x: 53,\n                y: 22,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: 15\n            },\n            {\n                id: \"a-ur2\",\n                x: 63,\n                y: 38,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\",\n                rotation: 15\n            },\n            // Cross bar\n            {\n                id: \"a-cross1\",\n                x: 35,\n                y: 50,\n                width: 12,\n                height: 8,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-cross2\",\n                x: 53,\n                y: 50,\n                width: 12,\n                height: 8,\n                shape: \"rectangle\"\n            },\n            // Lower left leg\n            {\n                id: \"a-ll1\",\n                x: 15,\n                y: 65,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-ll2\",\n                x: 15,\n                y: 82,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Lower right leg\n            {\n                id: \"a-lr1\",\n                x: 73,\n                y: 65,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"a-lr2\",\n                x: 73,\n                y: 82,\n                width: 12,\n                height: 15,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Number '1' Template\n    {\n        id: \"number-1\",\n        name: \"Number 1\",\n        description: \"Number 1 shaped photo collage\",\n        category: \"number\",\n        thumbnail: \"\",\n        canvasWidth: 400,\n        canvasHeight: 800,\n        backgroundColor: \"#fff5ee\",\n        slots: [\n            // Top diagonal\n            {\n                id: \"num1-top\",\n                x: 25,\n                y: 5,\n                width: 15,\n                height: 12,\n                shape: \"rectangle\",\n                rotation: 45\n            },\n            // Main vertical line\n            {\n                id: \"num1-1\",\n                x: 40,\n                y: 15,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-2\",\n                x: 40,\n                y: 32,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-3\",\n                x: 40,\n                y: 49,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-4\",\n                x: 40,\n                y: 66,\n                width: 20,\n                height: 15,\n                shape: \"rectangle\"\n            },\n            // Bottom base\n            {\n                id: \"num1-base1\",\n                x: 20,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-base2\",\n                x: 40,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            },\n            {\n                id: \"num1-base3\",\n                x: 60,\n                y: 83,\n                width: 20,\n                height: 12,\n                shape: \"rectangle\"\n            }\n        ]\n    },\n    // Circular Pattern Template\n    {\n        id: \"circle-pattern\",\n        name: \"Circle Pattern\",\n        description: \"Circular arrangement of photos\",\n        category: \"shape\",\n        thumbnail: \"\",\n        canvasWidth: 800,\n        canvasHeight: 800,\n        backgroundColor: \"#f5f5f5\",\n        slots: [\n            // Center circle\n            {\n                id: \"center\",\n                x: 37.5,\n                y: 37.5,\n                width: 25,\n                height: 25,\n                shape: \"circle\"\n            },\n            // Inner ring (8 photos)\n            {\n                id: \"inner-1\",\n                x: 50,\n                y: 15,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-2\",\n                x: 70,\n                y: 25,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-3\",\n                x: 80,\n                y: 50,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-4\",\n                x: 70,\n                y: 75,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-5\",\n                x: 50,\n                y: 85,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-6\",\n                x: 25,\n                y: 75,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-7\",\n                x: 15,\n                y: 50,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            {\n                id: \"inner-8\",\n                x: 25,\n                y: 25,\n                width: 15,\n                height: 15,\n                shape: \"circle\"\n            },\n            // Outer ring (4 photos)\n            {\n                id: \"outer-1\",\n                x: 50,\n                y: 2,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-2\",\n                x: 88,\n                y: 50,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-3\",\n                x: 50,\n                y: 88,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            },\n            {\n                id: \"outer-4\",\n                x: 2,\n                y: 50,\n                width: 12,\n                height: 12,\n                shape: \"circle\"\n            }\n        ]\n    }\n];\nconst getTemplateById = (id)=>{\n    return templates.find((template)=>template.id === id);\n};\nconst getTemplatesByCategory = (category)=>{\n    return templates.filter((template)=>template.category === category);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/data/templates.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL3Bob3RvLWNvbGxhZ2UtYXBwLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@radix-ui","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Ftemplates%2Fpage&page=%2Ftemplates%2Fpage&appPaths=%2Ftemplates%2Fpage&pagePath=private-next-app-dir%2Ftemplates%2Fpage.tsx&appDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CWorkSpace%5CphotoCollage%5Cphoto-collage-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();