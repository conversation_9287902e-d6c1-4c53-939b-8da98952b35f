"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/SimpleEditor.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/templates */ \"(app-pages-browser)/./src/data/templates.ts\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _StructuredData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./StructuredData */ \"(app-pages-browser)/./src/components/StructuredData.tsx\");\n/* harmony import */ var _Breadcrumb__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./Breadcrumb */ \"(app-pages-browser)/./src/components/Breadcrumb.tsx\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst SimpleEditor = (param)=>{\n    let { initialTemplateId } = param;\n    _s();\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTemplateId ? _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.find((t)=>t.id === initialTemplateId) || _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates[0] : _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates[0]);\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditor, setShowEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const breadcrumbItems = [\n        {\n            label: \"Home\",\n            href: \"/\"\n        },\n        {\n            label: \"Photo Collage Maker\",\n            current: true\n        }\n    ];\n    const categoryIcons = {\n        grid: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        heart: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        letter: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        number: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        shape: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    };\n    // Photo upload functionality\n    const createPhotoFromFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            const img = new Image();\n            reader.onload = (e)=>{\n                var _e_target;\n                const url = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                img.onload = ()=>{\n                    const photo = {\n                        id: \"photo-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9)),\n                        file,\n                        url,\n                        width: img.width,\n                        height: img.height\n                    };\n                    resolve(photo);\n                };\n                img.onerror = ()=>reject(new Error(\"Failed to load image\"));\n                img.src = url;\n            };\n            reader.onerror = ()=>reject(new Error(\"Failed to read file\"));\n            reader.readAsDataURL(file);\n        });\n    }, []);\n    const handleFileSelect = async (e)=>{\n        const files = e.target.files;\n        if (!files || files.length === 0) return;\n        setIsUploading(true);\n        const newPhotos = [];\n        for(let i = 0; i < files.length; i++){\n            try {\n                const photo = await createPhotoFromFile(files[i]);\n                newPhotos.push(photo);\n            } catch (error) {\n                console.error(\"Failed to process image:\", error);\n            }\n        }\n        setUploadedPhotos((prev)=>[\n                ...prev,\n                ...newPhotos\n            ]);\n        setIsUploading(false);\n        // Reset input\n        e.target.value = \"\";\n        // Auto-place photos if template is selected\n        if (selectedTemplate && newPhotos.length > 0) {\n            autoPlacePhotos(newPhotos);\n        }\n    };\n    const autoPlacePhotos = (newPhotos)=>{\n        if (!selectedTemplate) return;\n        const availableSlots = selectedTemplate.slots.filter((slot)=>!placedPhotos.some((placed)=>placed.slotId === slot.id));\n        const newPlacedPhotos = [];\n        newPhotos.forEach((photo, index)=>{\n            if (index < availableSlots.length) {\n                const slot = availableSlots[index];\n                newPlacedPhotos.push({\n                    photoId: photo.id,\n                    slotId: slot.id,\n                    x: 50,\n                    y: 50,\n                    scale: 1.0,\n                    rotation: 0\n                });\n            }\n        });\n        if (newPlacedPhotos.length > 0) {\n            setPlacedPhotos((prev)=>[\n                    ...prev,\n                    ...newPlacedPhotos\n                ]);\n        }\n    };\n    const handleStartEditing = ()=>{\n        if (selectedTemplate) {\n            setShowEditor(true);\n        }\n    };\n    const handleBackToTemplates = ()=>{\n        setShowEditor(false);\n    };\n    // Render editor view if user has started editing\n    if (showEditor && selectedTemplate) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white border-b border-gray-200 px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleBackToTemplates,\n                                            children: \"← Back to Templates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: [\n                                                \"Editing: \",\n                                                selectedTemplate.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Share\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            disabled: placedPhotos.length === 0,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Download\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-[calc(100vh-80px)]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Upload Photos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-8 h-8 text-gray-400 mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-2\",\n                                            children: \"Drag photos here or click to upload\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            onClick: ()=>{\n                                                var _fileInputRef_current;\n                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                            },\n                                            disabled: isUploading,\n                                            children: isUploading ? \"Uploading...\" : \"Choose Files\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            multiple: true,\n                                            accept: \"image/*\",\n                                            onChange: handleFileSelect,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined),\n                                uploadedPhotos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium mb-2\",\n                                            children: [\n                                                \"Uploaded Photos (\",\n                                                uploadedPhotos.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-2\",\n                                            children: uploadedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: photo.url,\n                                                            alt: \"Uploaded photo\",\n                                                            className: \"w-full h-20 object-cover rounded border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setUploadedPhotos((prev)=>prev.filter((p)=>p.id !== photo.id)),\n                                                            className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, photo.id, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-8 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 text-center\",\n                                        children: [\n                                            selectedTemplate.name,\n                                            \" Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative bg-gray-100 rounded border-2 border-gray-300\",\n                                        style: {\n                                            width: Math.min(600, selectedTemplate.canvasWidth * 0.5),\n                                            height: Math.min(400, selectedTemplate.canvasHeight * 0.5)\n                                        },\n                                        children: selectedTemplate.slots.map((slot)=>{\n                                            const placedPhoto = placedPhotos.find((p)=>p.slotId === slot.id);\n                                            const uploadedPhoto = placedPhoto ? uploadedPhotos.find((p)=>p.id === placedPhoto.photoId) : null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute border-2 border-dashed border-gray-400 bg-gray-50 flex items-center justify-center text-gray-500 text-sm\",\n                                                style: {\n                                                    left: \"\".concat(slot.x / selectedTemplate.canvasWidth * 100, \"%\"),\n                                                    top: \"\".concat(slot.y / selectedTemplate.canvasHeight * 100, \"%\"),\n                                                    width: \"\".concat(slot.width / selectedTemplate.canvasWidth * 100, \"%\"),\n                                                    height: \"\".concat(slot.height / selectedTemplate.canvasHeight * 100, \"%\")\n                                                },\n                                                children: uploadedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: uploadedPhoto.url,\n                                                    alt: \"Placed photo\",\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Photo \",\n                                                        slot.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, slot.id, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    placedPhotos.length < selectedTemplate.slots.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-gray-600 mt-4\",\n                                        children: [\n                                            \"Upload \",\n                                            selectedTemplate.slots.length - placedPhotos.length,\n                                            \" more photos to fill all slots\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StructuredData__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                type: \"WebApplication\",\n                data: _StructuredData__WEBPACK_IMPORTED_MODULE_6__.WebApplicationSchema\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StructuredData__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                type: \"HowTo\",\n                data: _StructuredData__WEBPACK_IMPORTED_MODULE_6__.HowToSchema\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StructuredData__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                type: \"FAQPage\",\n                data: _StructuredData__WEBPACK_IMPORTED_MODULE_6__.FAQSchema\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 px-4 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Photo Collage Maker\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Share\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            disabled: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Download\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Breadcrumb__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            items: breadcrumbItems\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-6xl mx-auto p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-gray-800 mb-4\",\n                                children: \"Create Beautiful Photo Collages Online Free\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                children: \"Design stunning photo collages with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates. Upload photos and download high-quality collages instantly - no registration required!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ 100% Free\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ No Registration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ High Quality Downloads\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ Multiple Templates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold text-gray-800\",\n                                                children: \"Choose Your Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-1\",\n                                                children: \"Select from our collection of beautiful collage templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"default\",\n                                        className: \"text-sm\",\n                                        children: [\n                                            selectedTemplate.name,\n                                            \" Selected\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                                children: _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.map((template)=>{\n                                    const IconComponent = categoryIcons[template.category] || _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"];\n                                    const isSelected = (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.id) === template.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"cursor-pointer transition-all duration-200 hover:shadow-lg \".concat(isSelected ? \"ring-2 ring-blue-500 ring-offset-2\" : \"\"),\n                                        onClick: ()=>setSelectedTemplate(template),\n                                        role: \"button\",\n                                        tabIndex: 0,\n                                        \"aria-label\": \"Select \".concat(template.name, \" template with \").concat(template.slots.length, \" photo slots\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg mb-3 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"w-8 h-8 text-gray-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 font-medium\",\n                                                                children: [\n                                                                    template.slots.length,\n                                                                    \" Photos\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 text-sm\",\n                                                            children: template.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600 line-clamp-2\",\n                                                            children: template.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs capitalize\",\n                                                                    children: template.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-blue-600 text-xs font-medium\",\n                                                                    children: \"✓ Selected\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, template.id, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined),\n                    selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                                            children: [\n                                                \"Ready to Create: \",\n                                                selectedTemplate.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: selectedTemplate.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: selectedTemplate.slots.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Photo Slots\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: selectedTemplate.canvasWidth\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 396,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Width (px)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: selectedTemplate.canvasHeight\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Height (px)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-orange-600 capitalize\",\n                                                            children: selectedTemplate.category\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3 justify-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setSelectedTemplate(null),\n                                        children: \"Choose Different Template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        className: \"bg-blue-600 hover:bg-blue-700\",\n                                        onClick: handleStartEditing,\n                                        children: \"Start Creating Collage →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-800 mb-6 text-center\",\n                                children: \"How It Works\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-600 font-bold\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Choose Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Select from heart shapes, grids, letters, and numbers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-bold\",\n                                                    children: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Upload Photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Drag and drop or click to upload your favorite photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-purple-600 font-bold\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Arrange & Edit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Position, scale, and rotate photos to perfect your collage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-orange-600 font-bold\",\n                                                    children: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Save your high-quality collage as PNG image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-800 mb-6 text-center\",\n                                children: \"Why Choose Our Collage Maker?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"100% Free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Create unlimited collages without any cost or hidden fees\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Multiple Templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Choose from hearts, grids, letters, numbers, and custom shapes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-8 h-8 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"High Quality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Download your collages in high resolution perfect for printing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 458,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SimpleEditor, \"5yL2KtlK+suXqE7lUZCNT1AkwVE=\");\n_c = SimpleEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SimpleEditor);\nvar _c;\n$RefreshReg$(_c, \"SimpleEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleEditor.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/StructuredData.tsx":
/*!*******************************************!*\
  !*** ./src/components/StructuredData.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreativeWorkSchema: function() { return /* binding */ CreativeWorkSchema; },\n/* harmony export */   FAQSchema: function() { return /* binding */ FAQSchema; },\n/* harmony export */   HowToSchema: function() { return /* binding */ HowToSchema; },\n/* harmony export */   WebApplicationSchema: function() { return /* binding */ WebApplicationSchema; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default,WebApplicationSchema,HowToSchema,FAQSchema,CreativeWorkSchema auto */ var _s = $RefreshSig$();\n\nconst StructuredData = (param)=>{\n    let { type, data } = param;\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const script = document.createElement(\"script\");\n        script.type = \"application/ld+json\";\n        script.text = JSON.stringify({\n            \"@context\": \"https://schema.org\",\n            \"@type\": type,\n            ...data\n        });\n        document.head.appendChild(script);\n        return ()=>{\n            document.head.removeChild(script);\n        };\n    }, [\n        type,\n        data\n    ]);\n    return null;\n};\n_s(StructuredData, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = StructuredData;\n/* harmony default export */ __webpack_exports__[\"default\"] = (StructuredData);\n// Predefined structured data for common use cases\nconst WebApplicationSchema = {\n    name: \"Photo Collage Maker\",\n    description: \"Create stunning photo collages online for free with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates.\",\n    url: \"https://photocollagemakerpro.com\",\n    applicationCategory: \"DesignApplication\",\n    operatingSystem: \"Web Browser\",\n    offers: {\n        \"@type\": \"Offer\",\n        price: \"0\",\n        priceCurrency: \"USD\",\n        availability: \"https://schema.org/InStock\"\n    },\n    featureList: [\n        \"Free photo collage maker\",\n        \"Multiple template categories\",\n        \"Heart-shaped collages\",\n        \"Grid layouts\",\n        \"Letter and number templates\",\n        \"Drag and drop interface\",\n        \"High-quality downloads\",\n        \"No registration required\"\n    ],\n    screenshot: \"https://photocollagemakerpro.com/screenshot.jpg\",\n    softwareVersion: \"1.0.0\",\n    aggregateRating: {\n        \"@type\": \"AggregateRating\",\n        ratingValue: \"4.8\",\n        ratingCount: \"1250\",\n        bestRating: \"5\",\n        worstRating: \"1\"\n    },\n    author: {\n        \"@type\": \"Organization\",\n        name: \"Photo Collage Maker Team\"\n    },\n    publisher: {\n        \"@type\": \"Organization\",\n        name: \"Photo Collage Maker\",\n        logo: {\n            \"@type\": \"ImageObject\",\n            url: \"https://photocollagemakerpro.com/logo.png\"\n        }\n    }\n};\nconst HowToSchema = {\n    name: \"How to Create a Photo Collage\",\n    description: \"Step-by-step guide to creating beautiful photo collages using our online tool\",\n    image: \"https://photocollagemakerpro.com/how-to-guide.jpg\",\n    totalTime: \"PT5M\",\n    estimatedCost: {\n        \"@type\": \"MonetaryAmount\",\n        currency: \"USD\",\n        value: \"0\"\n    },\n    supply: [\n        {\n            \"@type\": \"HowToSupply\",\n            name: \"Digital Photos\"\n        }\n    ],\n    tool: [\n        {\n            \"@type\": \"HowToTool\",\n            name: \"Photo Collage Maker Web Application\"\n        }\n    ],\n    step: [\n        {\n            \"@type\": \"HowToStep\",\n            name: \"Choose Template\",\n            text: \"Select a template from our gallery of heart shapes, grids, letters, and numbers\",\n            image: \"https://photocollagemakerpro.com/step1.jpg\"\n        },\n        {\n            \"@type\": \"HowToStep\",\n            name: \"Upload Photos\",\n            text: \"Upload your photos by dragging and dropping or clicking to select files\",\n            image: \"https://photocollagemakerpro.com/step2.jpg\"\n        },\n        {\n            \"@type\": \"HowToStep\",\n            name: \"Arrange Photos\",\n            text: \"Drag photos into template slots and adjust position, scale, and rotation\",\n            image: \"https://photocollagemakerpro.com/step3.jpg\"\n        },\n        {\n            \"@type\": \"HowToStep\",\n            name: \"Download Collage\",\n            text: \"Preview your collage and download as a high-quality PNG image\",\n            image: \"https://photocollagemakerpro.com/step4.jpg\"\n        }\n    ]\n};\nconst FAQSchema = {\n    mainEntity: [\n        {\n            \"@type\": \"Question\",\n            name: \"Is the photo collage maker free to use?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"Yes, our photo collage maker is completely free to use. You can create unlimited collages without any cost or registration required.\"\n            }\n        },\n        {\n            \"@type\": \"Question\",\n            name: \"What image formats are supported?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"We support JPEG, PNG, and WebP image formats. Maximum file size is 10MB per image.\"\n            }\n        },\n        {\n            \"@type\": \"Question\",\n            name: \"How many photos can I add to a collage?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"The number of photos depends on the template you choose. Our templates range from 4 photos to 16 photos per collage.\"\n            }\n        },\n        {\n            \"@type\": \"Question\",\n            name: \"Can I download my collage in high quality?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"Yes, you can download your finished collage as a high-quality PNG image that's perfect for printing or sharing online.\"\n            }\n        },\n        {\n            \"@type\": \"Question\",\n            name: \"Do I need to create an account?\",\n            acceptedAnswer: {\n                \"@type\": \"Answer\",\n                text: \"No account creation is required. You can start creating collages immediately without any registration.\"\n            }\n        }\n    ]\n};\nconst CreativeWorkSchema = {\n    name: \"Photo Collage Templates\",\n    description: \"Collection of creative photo collage templates including hearts, grids, letters, and shapes\",\n    creator: {\n        \"@type\": \"Organization\",\n        name: \"Photo Collage Maker Team\"\n    },\n    dateCreated: \"2024-01-01\",\n    dateModified: new Date().toISOString().split(\"T\")[0],\n    genre: \"Design Template\",\n    keywords: [\n        \"photo collage\",\n        \"template\",\n        \"heart shape\",\n        \"grid layout\",\n        \"letter template\",\n        \"number template\",\n        \"photo arrangement\"\n    ],\n    license: \"https://creativecommons.org/licenses/by/4.0/\",\n    usageInfo: \"Free for personal and commercial use\"\n};\nvar _c;\n$RefreshReg$(_c, \"StructuredData\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/StructuredData.tsx\n"));

/***/ })

});