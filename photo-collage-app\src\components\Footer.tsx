'use client';

import Link from 'next/link';
import { Heart, Grid, Type, Hash, Circle } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  const categoryLinks = [
    { href: '/templates/grid', label: 'Grid Templates', icon: Grid },
    { href: '/templates/heart', label: 'Heart Templates', icon: Heart },
    { href: '/templates/letter', label: 'Letter Templates', icon: Type },
    { href: '/templates/number', label: 'Number Templates', icon: Hash },
    { href: '/templates/shape', label: 'Shape Templates', icon: Circle },
  ];

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and Description */}
          <div className="col-span-1 md:col-span-1">
            <Link href="/" className="flex items-center">
              <span className="text-xl font-bold text-white">Photo Collage Maker Pro</span>
            </Link>
            <p className="mt-4 text-gray-400 text-sm">
              Create beautiful photo collages online for free. Choose from 50+ templates and download high-quality results instantly.
            </p>
          </div>

          {/* Templates */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Templates</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/templates" className="text-gray-400 hover:text-white transition-colors">
                  All Templates
                </Link>
              </li>
              {categoryLinks.map((link) => (
                <li key={link.href}>
                  <Link href={link.href} className="text-gray-400 hover:text-white transition-colors">
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-400 hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/faq" className="text-gray-400 hover:text-white transition-colors">
                  FAQ
                </Link>
              </li>
              <li>
                <Link href="/templates" className="text-gray-400 hover:text-white transition-colors">
                  Browse Templates
                </Link>
              </li>
              <li>
                <Link href="/" className="text-gray-400 hover:text-white transition-colors">
                  Create Collage
                </Link>
              </li>
            </ul>
          </div>

          {/* Popular Templates */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Popular Templates</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/templates/grid/grid-4x4" className="text-gray-400 hover:text-white transition-colors">
                  4×4 Grid
                </Link>
              </li>
              <li>
                <Link href="/templates/heart/heart-classic" className="text-gray-400 hover:text-white transition-colors">
                  Classic Heart
                </Link>
              </li>
              <li>
                <Link href="/templates/letter/letter-a" className="text-gray-400 hover:text-white transition-colors">
                  Letter A
                </Link>
              </li>
              <li>
                <Link href="/templates/number/number-1" className="text-gray-400 hover:text-white transition-colors">
                  Number 1
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {currentYear} Photo Collage Maker Pro. All rights reserved.
            </p>
            <div className="mt-4 md:mt-0">
              <ul className="flex space-x-6">
                <li>
                  <Link href="/sitemap.xml" className="text-gray-400 hover:text-white transition-colors text-sm">
                    Sitemap
                  </Link>
                </li>
                <li>
                  <Link href="/faq" className="text-gray-400 hover:text-white transition-colors text-sm">
                    Help
                  </Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
