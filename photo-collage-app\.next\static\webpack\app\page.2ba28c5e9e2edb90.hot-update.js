"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleEditor.tsx":
/*!*****************************************!*\
  !*** ./src/components/SimpleEditor.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_templates__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/templates */ \"(app-pages-browser)/./src/data/templates.ts\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _Breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Breadcrumb */ \"(app-pages-browser)/./src/components/Breadcrumb.tsx\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Circle,Download,Grid,Hash,Heart,Share2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst SimpleEditor = (param)=>{\n    let { initialTemplateId } = param;\n    _s();\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTemplateId ? _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.find((t)=>t.id === initialTemplateId) || _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates[0] : _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates[0]);\n    const [uploadedPhotos, setUploadedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [placedPhotos, setPlacedPhotos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditor, setShowEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const breadcrumbItems = [\n        {\n            label: \"Home\",\n            href: \"/\"\n        },\n        {\n            label: \"Photo Collage Maker\",\n            current: true\n        }\n    ];\n    const categoryIcons = {\n        grid: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        heart: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        letter: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        number: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        shape: _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    };\n    // Photo upload functionality\n    const createPhotoFromFile = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            const img = new Image();\n            reader.onload = (e)=>{\n                var _e_target;\n                const url = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                img.onload = ()=>{\n                    const photo = {\n                        id: \"photo-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9)),\n                        file,\n                        url,\n                        width: img.width,\n                        height: img.height\n                    };\n                    resolve(photo);\n                };\n                img.onerror = ()=>reject(new Error(\"Failed to load image\"));\n                img.src = url;\n            };\n            reader.onerror = ()=>reject(new Error(\"Failed to read file\"));\n            reader.readAsDataURL(file);\n        });\n    }, []);\n    const handleFileSelect = async (e)=>{\n        const files = e.target.files;\n        if (!files || files.length === 0) return;\n        setIsUploading(true);\n        const newPhotos = [];\n        for(let i = 0; i < files.length; i++){\n            try {\n                const photo = await createPhotoFromFile(files[i]);\n                newPhotos.push(photo);\n            } catch (error) {\n                console.error(\"Failed to process image:\", error);\n            }\n        }\n        setUploadedPhotos((prev)=>[\n                ...prev,\n                ...newPhotos\n            ]);\n        setIsUploading(false);\n        // Reset input\n        e.target.value = \"\";\n        // Auto-place photos if template is selected\n        if (selectedTemplate && newPhotos.length > 0) {\n            autoPlacePhotos(newPhotos);\n        }\n    };\n    const autoPlacePhotos = (newPhotos)=>{\n        if (!selectedTemplate) return;\n        const availableSlots = selectedTemplate.slots.filter((slot)=>!placedPhotos.some((placed)=>placed.slotId === slot.id));\n        const newPlacedPhotos = [];\n        newPhotos.forEach((photo, index)=>{\n            if (index < availableSlots.length) {\n                const slot = availableSlots[index];\n                newPlacedPhotos.push({\n                    photoId: photo.id,\n                    slotId: slot.id,\n                    x: 50,\n                    y: 50,\n                    scale: 1.0,\n                    rotation: 0\n                });\n            }\n        });\n        if (newPlacedPhotos.length > 0) {\n            setPlacedPhotos((prev)=>[\n                    ...prev,\n                    ...newPlacedPhotos\n                ]);\n        }\n    };\n    const handleStartEditing = ()=>{\n        if (selectedTemplate) {\n            setShowEditor(true);\n        }\n    };\n    const handleBackToTemplates = ()=>{\n        setShowEditor(false);\n    };\n    // Render editor view if user has started editing\n    if (showEditor && selectedTemplate) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"bg-white border-b border-gray-200 px-4 py-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: handleBackToTemplates,\n                                            children: \"← Back to Templates\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-xl font-bold text-gray-900\",\n                                            children: [\n                                                \"Editing: \",\n                                                selectedTemplate.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Share\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            disabled: placedPhotos.length === 0,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Download\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-[calc(100vh-80px)]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Upload Photos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"w-8 h-8 text-gray-400 mx-auto mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-2\",\n                                            children: \"Drag photos here or click to upload\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            onClick: ()=>{\n                                                var _fileInputRef_current;\n                                                return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                            },\n                                            disabled: isUploading,\n                                            children: isUploading ? \"Uploading...\" : \"Choose Files\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: fileInputRef,\n                                            type: \"file\",\n                                            multiple: true,\n                                            accept: \"image/*\",\n                                            onChange: handleFileSelect,\n                                            className: \"hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined),\n                                uploadedPhotos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium mb-2\",\n                                            children: [\n                                                \"Uploaded Photos (\",\n                                                uploadedPhotos.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-2\",\n                                            children: uploadedPhotos.map((photo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: photo.url,\n                                                            alt: \"Uploaded photo\",\n                                                            className: \"w-full h-20 object-cover rounded border\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setUploadedPhotos((prev)=>prev.filter((p)=>p.id !== photo.id)),\n                                                            className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, photo.id, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-8 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-lg p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4 text-center\",\n                                        children: [\n                                            selectedTemplate.name,\n                                            \" Preview\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative bg-gray-100 rounded border-2 border-gray-300\",\n                                        style: {\n                                            width: Math.min(600, selectedTemplate.canvasWidth * 0.5),\n                                            height: Math.min(400, selectedTemplate.canvasHeight * 0.5)\n                                        },\n                                        children: selectedTemplate.slots.map((slot)=>{\n                                            const placedPhoto = placedPhotos.find((p)=>p.slotId === slot.id);\n                                            const uploadedPhoto = placedPhoto ? uploadedPhotos.find((p)=>p.id === placedPhoto.photoId) : null;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute border-2 border-dashed border-gray-400 bg-gray-50 flex items-center justify-center text-gray-500 text-sm\",\n                                                style: {\n                                                    left: \"\".concat(slot.x / selectedTemplate.canvasWidth * 100, \"%\"),\n                                                    top: \"\".concat(slot.y / selectedTemplate.canvasHeight * 100, \"%\"),\n                                                    width: \"\".concat(slot.width / selectedTemplate.canvasWidth * 100, \"%\"),\n                                                    height: \"\".concat(slot.height / selectedTemplate.canvasHeight * 100, \"%\")\n                                                },\n                                                children: uploadedPhoto ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: uploadedPhoto.url,\n                                                    alt: \"Placed photo\",\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Photo \",\n                                                        slot.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, slot.id, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    placedPhotos.length < selectedTemplate.slots.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-gray-600 mt-4\",\n                                        children: [\n                                            \"Upload \",\n                                            selectedTemplate.slots.length - placedPhotos.length,\n                                            \" more photos to fill all slots\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200 px-4 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Photo Collage Maker\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Share\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            size: \"sm\",\n                                            disabled: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Download\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Breadcrumb__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            items: breadcrumbItems\n                        }, void 0, false, {\n                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-6xl mx-auto p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-bold text-gray-800 mb-4\",\n                                children: \"Create Beautiful Photo Collages Online Free\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                children: \"Design stunning photo collages with our easy-to-use editor. Choose from heart shapes, grids, letters, numbers and custom templates. Upload photos and download high-quality collages instantly - no registration required!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap justify-center gap-4 text-sm text-gray-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ 100% Free\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ No Registration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ High Quality Downloads\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"✓ Multiple Templates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold text-gray-800\",\n                                                children: \"Choose Your Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mt-1\",\n                                                children: \"Select from our collection of beautiful collage templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                        variant: \"default\",\n                                        className: \"text-sm\",\n                                        children: [\n                                            selectedTemplate.name,\n                                            \" Selected\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                                children: _data_templates__WEBPACK_IMPORTED_MODULE_2__.templates.map((template)=>{\n                                    const IconComponent = categoryIcons[template.category] || _barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"];\n                                    const isSelected = (selectedTemplate === null || selectedTemplate === void 0 ? void 0 : selectedTemplate.id) === template.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"cursor-pointer transition-all duration-200 hover:shadow-lg \".concat(isSelected ? \"ring-2 ring-blue-500 ring-offset-2\" : \"\"),\n                                        onClick: ()=>setSelectedTemplate(template),\n                                        role: \"button\",\n                                        tabIndex: 0,\n                                        \"aria-label\": \"Select \".concat(template.name, \" template with \").concat(template.slots.length, \" photo slots\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-square bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg mb-3 flex items-center justify-center border-2 border-dashed border-gray-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                className: \"w-8 h-8 text-gray-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 font-medium\",\n                                                                children: [\n                                                                    template.slots.length,\n                                                                    \" Photos\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-gray-900 text-sm\",\n                                                            children: template.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600 line-clamp-2\",\n                                                            children: template.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"text-xs capitalize\",\n                                                                    children: template.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                    lineNumber: 360,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-blue-600 text-xs font-medium\",\n                                                                    children: \"✓ Selected\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                                    lineNumber: 364,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, template.id, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 19\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, undefined),\n                    selectedTemplate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-800 mb-2\",\n                                            children: [\n                                                \"Ready to Create: \",\n                                                selectedTemplate.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: selectedTemplate.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-600\",\n                                                            children: selectedTemplate.slots.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Photo Slots\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600\",\n                                                            children: selectedTemplate.canvasWidth\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Width (px)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-purple-600\",\n                                                            children: selectedTemplate.canvasHeight\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Height (px)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-3 bg-gray-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-orange-600 capitalize\",\n                                                            children: selectedTemplate.category\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: \"Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3 justify-end\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>setSelectedTemplate(null),\n                                        children: \"Choose Different Template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        className: \"bg-blue-600 hover:bg-blue-700\",\n                                        onClick: handleStartEditing,\n                                        children: \"Start Creating Collage →\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 408,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-800 mb-6 text-center\",\n                                children: \"How It Works\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-600 font-bold\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Choose Template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Select from heart shapes, grids, letters, and numbers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-bold\",\n                                                    children: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Upload Photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Drag and drop or click to upload your favorite photos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-purple-600 font-bold\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Arrange & Edit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Position, scale, and rotate photos to perfect your collage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-orange-600 font-bold\",\n                                                    children: \"4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Download\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Save your high-quality collage as PNG image\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"bg-white rounded-lg shadow-md p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-800 mb-6 text-center\",\n                                children: \"Why Choose Our Collage Maker?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-3 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-8 h-8 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"100% Free\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Create unlimited collages without any cost or hidden fees\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-8 h-8 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"Multiple Templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Choose from hearts, grids, letters, numbers, and custom shapes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Circle_Download_Grid_Hash_Heart_Share2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-8 h-8 text-purple-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-2\",\n                                                children: \"High Quality\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"Download your collages in high resolution perfect for printing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\WorkSpace\\\\photoCollage\\\\photo-collage-app\\\\src\\\\components\\\\SimpleEditor.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SimpleEditor, \"5yL2KtlK+suXqE7lUZCNT1AkwVE=\");\n_c = SimpleEditor;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SimpleEditor);\nvar _c;\n$RefreshReg$(_c, \"SimpleEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleEditor.tsx\n"));

/***/ })

});