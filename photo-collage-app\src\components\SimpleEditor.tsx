'use client';

import React, { useState, useRef, useCallback } from 'react';
import { CollageTemplate, UploadedPhoto, PlacedPhoto } from '@/types/template';
import { templates } from '@/data/templates';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import StructuredData, { WebApplicationSchema, HowToSchema, FAQSchema } from './StructuredData';
import Breadcrumb from './Breadcrumb';
import { Heart, Grid, Type, Hash, Circle, Download, Share2, Upload, X } from 'lucide-react';

interface SimpleEditorProps {
  initialTemplateId?: string;
}

const SimpleEditor: React.FC<SimpleEditorProps> = ({ initialTemplateId }) => {
  const [selectedTemplate, setSelectedTemplate] = useState<CollageTemplate | null>(
    initialTemplateId ? templates.find(t => t.id === initialTemplateId) || templates[0] : templates[0]
  );
  const [uploadedPhotos, setUploadedPhotos] = useState<UploadedPhoto[]>([]);
  const [placedPhotos, setPlacedPhotos] = useState<PlacedPhoto[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [showEditor, setShowEditor] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Photo Collage Maker', current: true }
  ];

  const categoryIcons = {
    grid: Grid,
    heart: Heart,
    letter: Type,
    number: Hash,
    shape: Circle
  };

  // Photo upload functionality
  const createPhotoFromFile = useCallback(async (file: File): Promise<UploadedPhoto> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      const img = new Image();

      reader.onload = (e) => {
        const url = e.target?.result as string;
        img.onload = () => {
          const photo: UploadedPhoto = {
            id: `photo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            file,
            url,
            width: img.width,
            height: img.height
          };
          resolve(photo);
        };
        img.onerror = () => reject(new Error('Failed to load image'));
        img.src = url;
      };

      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(file);
    });
  }, []);

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);
    const newPhotos: UploadedPhoto[] = [];

    for (let i = 0; i < files.length; i++) {
      try {
        const photo = await createPhotoFromFile(files[i]);
        newPhotos.push(photo);
      } catch (error) {
        console.error('Failed to process image:', error);
      }
    }

    setUploadedPhotos(prev => [...prev, ...newPhotos]);
    setIsUploading(false);

    // Reset input
    e.target.value = '';

    // Auto-place photos if template is selected
    if (selectedTemplate && newPhotos.length > 0) {
      autoPlacePhotos(newPhotos);
    }
  };

  const autoPlacePhotos = (newPhotos: UploadedPhoto[]) => {
    if (!selectedTemplate) return;

    const availableSlots = selectedTemplate.slots.filter(slot =>
      !placedPhotos.some(placed => placed.slotId === slot.id)
    );

    const newPlacedPhotos: PlacedPhoto[] = [];

    newPhotos.forEach((photo, index) => {
      if (index < availableSlots.length) {
        const slot = availableSlots[index];
        newPlacedPhotos.push({
          photoId: photo.id,
          slotId: slot.id,
          x: 50,
          y: 50,
          scale: 1.0,
          rotation: 0
        });
      }
    });

    if (newPlacedPhotos.length > 0) {
      setPlacedPhotos(prev => [...prev, ...newPlacedPhotos]);
    }
  };

  const handleStartEditing = () => {
    if (selectedTemplate) {
      setShowEditor(true);
    }
  };

  const handleBackToTemplates = () => {
    setShowEditor(false);
  };

  // Render editor view if user has started editing
  if (showEditor && selectedTemplate) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Editor Header */}
        <header className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button variant="outline" size="sm" onClick={handleBackToTemplates}>
                  ← Back to Templates
                </Button>
                <h1 className="text-xl font-bold text-gray-900">
                  Editing: {selectedTemplate.name}
                </h1>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Share2 className="w-4 h-4 mr-1" />
                  Share
                </Button>
                <Button size="sm" disabled={placedPhotos.length === 0}>
                  <Download className="w-4 h-4 mr-1" />
                  Download
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Editor Content */}
        <div className="flex h-[calc(100vh-80px)]">
          {/* Left Sidebar - Photo Upload */}
          <div className="w-80 bg-white border-r border-gray-200 p-4 overflow-y-auto">
            <h2 className="text-lg font-semibold mb-4">Upload Photos</h2>

            {/* Upload Area */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-4">
              <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-2">
                Drag photos here or click to upload
              </p>
              <Button
                size="sm"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                {isUploading ? 'Uploading...' : 'Choose Files'}
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
              />
            </div>

            {/* Uploaded Photos */}
            {uploadedPhotos.length > 0 && (
              <div>
                <h3 className="font-medium mb-2">Uploaded Photos ({uploadedPhotos.length})</h3>
                <div className="grid grid-cols-2 gap-2">
                  {uploadedPhotos.map(photo => (
                    <div key={photo.id} className="relative group">
                      <img
                        src={photo.url}
                        alt="Uploaded photo"
                        className="w-full h-20 object-cover rounded border"
                      />
                      <button
                        onClick={() => setUploadedPhotos(prev => prev.filter(p => p.id !== photo.id))}
                        className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Center - Canvas */}
          <div className="flex-1 p-8 flex items-center justify-center">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-4 text-center">
                {selectedTemplate.name} Preview
              </h3>
              <div
                className="relative bg-gray-100 rounded border-2 border-gray-300"
                style={{
                  width: Math.min(600, selectedTemplate.canvasWidth * 0.5),
                  height: Math.min(400, selectedTemplate.canvasHeight * 0.5),
                }}
              >
                {/* Template slots */}
                {selectedTemplate.slots.map(slot => {
                  const placedPhoto = placedPhotos.find(p => p.slotId === slot.id);
                  const uploadedPhoto = placedPhoto ? uploadedPhotos.find(p => p.id === placedPhoto.photoId) : null;

                  return (
                    <div
                      key={slot.id}
                      className="absolute border-2 border-dashed border-gray-400 bg-gray-50 flex items-center justify-center text-gray-500 text-sm"
                      style={{
                        left: `${(slot.x / selectedTemplate.canvasWidth) * 100}%`,
                        top: `${(slot.y / selectedTemplate.canvasHeight) * 100}%`,
                        width: `${(slot.width / selectedTemplate.canvasWidth) * 100}%`,
                        height: `${(slot.height / selectedTemplate.canvasHeight) * 100}%`,
                      }}
                    >
                      {uploadedPhoto ? (
                        <img
                          src={uploadedPhoto.url}
                          alt="Placed photo"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <span>Photo {slot.id}</span>
                      )}
                    </div>
                  );
                })}
              </div>

              {placedPhotos.length < selectedTemplate.slots.length && (
                <p className="text-center text-gray-600 mt-4">
                  Upload {selectedTemplate.slots.length - placedPhotos.length} more photos to fill all slots
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* SEO Structured Data */}
      <StructuredData type="WebApplication" data={WebApplicationSchema} />
      <StructuredData type="HowTo" data={HowToSchema} />
      <StructuredData type="FAQPage" data={FAQSchema} />
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center justify-between mb-2">
            <h1 className="text-2xl font-bold text-gray-900">Photo Collage Maker</h1>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Share2 className="w-4 h-4 mr-1" />
                Share
              </Button>
              <Button size="sm" disabled>
                <Download className="w-4 h-4 mr-1" />
                Download
              </Button>
            </div>
          </div>
          <Breadcrumb items={breadcrumbItems} />
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto p-8">
          {/* Hero Section */}
          <section className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-800 mb-4">
              Create Beautiful Photo Collages Online Free
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Design stunning photo collages with our easy-to-use editor. Choose from heart shapes,
              grids, letters, numbers and custom templates. Upload photos and download high-quality
              collages instantly - no registration required!
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
              <span>✓ 100% Free</span>
              <span>✓ No Registration</span>
              <span>✓ High Quality Downloads</span>
              <span>✓ Multiple Templates</span>
            </div>
          </section>

          {/* Template Gallery */}
          <section className="bg-white rounded-lg shadow-md p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-2xl font-semibold text-gray-800">Choose Your Template</h2>
                <p className="text-gray-600 mt-1">Select from our collection of beautiful collage templates</p>
              </div>
              {selectedTemplate && (
                <Badge variant="default" className="text-sm">
                  {selectedTemplate.name} Selected
                </Badge>
              )}
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {templates.map(template => {
                const IconComponent = categoryIcons[template.category as keyof typeof categoryIcons] || Circle;
                const isSelected = selectedTemplate?.id === template.id;

                return (
                  <Card
                    key={template.id}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                      isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''
                    }`}
                    onClick={() => setSelectedTemplate(template)}
                    role="button"
                    tabIndex={0}
                    aria-label={`Select ${template.name} template with ${template.slots.length} photo slots`}
                  >
                    <CardContent className="p-4">
                      <div className="aspect-square bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg mb-3 flex items-center justify-center border-2 border-dashed border-gray-300">
                        <div className="text-center">
                          <IconComponent className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                          <div className="text-xs text-gray-500 font-medium">
                            {template.slots.length} Photos
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <h3 className="font-semibold text-gray-900 text-sm">{template.name}</h3>
                        <p className="text-xs text-gray-600 line-clamp-2">{template.description}</p>

                        <div className="flex items-center justify-between">
                          <Badge variant="outline" className="text-xs capitalize">
                            {template.category}
                          </Badge>
                          {isSelected && (
                            <div className="text-blue-600 text-xs font-medium">
                              ✓ Selected
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </section>

          {/* Selected Template Details */}
          {selectedTemplate && (
            <section className="bg-white rounded-lg shadow-md p-6 mb-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h2 className="text-xl font-semibold text-gray-800 mb-2">
                    Ready to Create: {selectedTemplate.name}
                  </h2>
                  <p className="text-gray-600 mb-4">{selectedTemplate.description}</p>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{selectedTemplate.slots.length}</div>
                      <div className="text-xs text-gray-600">Photo Slots</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{selectedTemplate.canvasWidth}</div>
                      <div className="text-xs text-gray-600">Width (px)</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{selectedTemplate.canvasHeight}</div>
                      <div className="text-xs text-gray-600">Height (px)</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600 capitalize">{selectedTemplate.category}</div>
                      <div className="text-xs text-gray-600">Category</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 justify-end">
                <Button variant="outline" onClick={() => setSelectedTemplate(null)}>
                  Choose Different Template
                </Button>
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700" onClick={handleStartEditing}>
                  Start Creating Collage →
                </Button>
              </div>
            </section>
          )}

          {/* How It Works Section */}
          <section className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">How It Works</h2>
            <div className="grid md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-blue-600 font-bold">1</span>
                </div>
                <h3 className="font-semibold mb-2">Choose Template</h3>
                <p className="text-sm text-gray-600">Select from heart shapes, grids, letters, and numbers</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-green-600 font-bold">2</span>
                </div>
                <h3 className="font-semibold mb-2">Upload Photos</h3>
                <p className="text-sm text-gray-600">Drag and drop or click to upload your favorite photos</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-purple-600 font-bold">3</span>
                </div>
                <h3 className="font-semibold mb-2">Arrange & Edit</h3>
                <p className="text-sm text-gray-600">Position, scale, and rotate photos to perfect your collage</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-orange-600 font-bold">4</span>
                </div>
                <h3 className="font-semibold mb-2">Download</h3>
                <p className="text-sm text-gray-600">Save your high-quality collage as PNG image</p>
              </div>
            </div>
          </section>

          {/* Features Section */}
          <section className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">Why Choose Our Collage Maker?</h2>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="font-semibold mb-2">100% Free</h3>
                <p className="text-sm text-gray-600">Create unlimited collages without any cost or hidden fees</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Grid className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="font-semibold mb-2">Multiple Templates</h3>
                <p className="text-sm text-gray-600">Choose from hearts, grids, letters, numbers, and custom shapes</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Download className="w-8 h-8 text-purple-600" />
                </div>
                <h3 className="font-semibold mb-2">High Quality</h3>
                <p className="text-sm text-gray-600">Download your collages in high resolution perfect for printing</p>
              </div>
            </div>
        </section>
      </main>
    </div>
  );
};

export default SimpleEditor;
